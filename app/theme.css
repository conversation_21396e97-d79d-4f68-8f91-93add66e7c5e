:root {
  --background: oklch(0.9735 0.0261 90.0953);
  --foreground: oklch(0.3092 0.0518 219.6516);
  --card: oklch(0.9306 0.0260 92.4020);
  --card-foreground: oklch(0.3092 0.0518 219.6516);
  --popover: oklch(0.9306 0.0260 92.4020);
  --popover-foreground: oklch(0.3092 0.0518 219.6516);
  --primary: oklch(0.5924 0.2025 355.8943);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.6437 0.1019 187.3840);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.6979 0.0159 196.7940);
  --muted-foreground: oklch(0.3092 0.0518 219.6516);
  --accent: oklch(0.5808 0.1732 39.5003);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.5863 0.2064 27.1172);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.6537 0.0197 205.2618);
  --input: oklch(0.6537 0.0197 205.2618);
  --ring: oklch(0.5924 0.2025 355.8943);
  --chart-1: oklch(0.6149 0.1394 244.9273);
  --chart-2: oklch(0.6437 0.1019 187.3840);
  --chart-3: oklch(0.5924 0.2025 355.8943);
  --chart-4: oklch(0.5808 0.1732 39.5003);
  --chart-5: oklch(0.5863 0.2064 27.1172);
  --sidebar: oklch(0.9735 0.0261 90.0953);
  --sidebar-foreground: oklch(0.3092 0.0518 219.6516);
  --sidebar-primary: oklch(0.5924 0.2025 355.8943);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.6437 0.1019 187.3840);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.6537 0.0197 205.2618);
  --sidebar-ring: oklch(0.5924 0.2025 355.8943);
  --font-sans: Outfit, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: Space Mono, monospace;
  --radius: 0.25rem;
  --shadow-2xs: 2px 2px 4px 0px hsl(196 83% 10% / 0.07);
  --shadow-xs: 2px 2px 4px 0px hsl(196 83% 10% / 0.07);
  --shadow-sm: 2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 1px 2px -1px hsl(196 83% 10% / 0.15);
  --shadow: 2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 1px 2px -1px hsl(196 83% 10% / 0.15);
  --shadow-md: 2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 2px 4px -1px hsl(196 83% 10% / 0.15);
  --shadow-lg: 2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 4px 6px -1px hsl(196 83% 10% / 0.15);
  --shadow-xl: 2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 8px 10px -1px hsl(196 83% 10% / 0.15);
  --shadow-2xl: 2px 2px 4px 0px hsl(196 83% 10% / 0.38);
}

.dark {
  --background: oklch(0.2673 0.0486 219.8169);
  --foreground: oklch(0.6979 0.0159 196.7940);
  --card: oklch(0.3092 0.0518 219.6516);
  --card-foreground: oklch(0.6979 0.0159 196.7940);
  --popover: oklch(0.3092 0.0518 219.6516);
  --popover-foreground: oklch(0.6979 0.0159 196.7940);
  --primary: oklch(0.5924 0.2025 355.8943);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.6437 0.1019 187.3840);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.5230 0.0283 219.1365);
  --muted-foreground: oklch(0.6979 0.0159 196.7940);
  --accent: oklch(0.5808 0.1732 39.5003);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.5863 0.2064 27.1172);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.5230 0.0283 219.1365);
  --input: oklch(0.5230 0.0283 219.1365);
  --ring: oklch(0.5924 0.2025 355.8943);
  --chart-1: oklch(0.6149 0.1394 244.9273);
  --chart-2: oklch(0.6437 0.1019 187.3840);
  --chart-3: oklch(0.5924 0.2025 355.8943);
  --chart-4: oklch(0.5808 0.1732 39.5003);
  --chart-5: oklch(0.5863 0.2064 27.1172);
  --sidebar: oklch(0.2673 0.0486 219.8169);
  --sidebar-foreground: oklch(0.6979 0.0159 196.7940);
  --sidebar-primary: oklch(0.5924 0.2025 355.8943);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.6437 0.1019 187.3840);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.5230 0.0283 219.1365);
  --sidebar-ring: oklch(0.5924 0.2025 355.8943);
  --font-sans: Outfit, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: Space Mono, monospace;
  --radius: 0.25rem;
  --shadow-2xs: 2px 2px 4px 0px hsl(196 83% 10% / 0.07);
  --shadow-xs: 2px 2px 4px 0px hsl(196 83% 10% / 0.07);
  --shadow-sm: 2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 1px 2px -1px hsl(196 83% 10% / 0.15);
  --shadow: 2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 1px 2px -1px hsl(196 83% 10% / 0.15);
  --shadow-md: 2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 2px 4px -1px hsl(196 83% 10% / 0.15);
  --shadow-lg: 2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 4px 6px -1px hsl(196 83% 10% / 0.15);
  --shadow-xl: 2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 8px 10px -1px hsl(196 83% 10% / 0.15);
  --shadow-2xl: 2px 2px 4px 0px hsl(196 83% 10% / 0.38);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}